buildscript {
    repositories {
        maven { url = 'https://maven.minecraftforge.net/' }
        mavenCentral()
    }
    dependencies {
        classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: '5.1.+', changing: true
        classpath 'com.github.jengelman.gradle.plugins:shadow:4.0.4'
        classpath 'org.spongepowered:mixingradle:0.7.+'
    }
}

plugins {
    id "com.jfrog.artifactory" version "4.26.1"
}

repositories {
    maven {
        url artifactory_contextUrl + '/ACsGuisRepo'
    }
    maven {
        url artifactory_contextUrl + '/DynamXRepo'
    }
    maven { url = 'https://repo.spongepowered.org/maven' }

    maven {
        url "https://cursemaven.com"
    }

    flatDir {
        dir 'libs'
    }
}

apply plugin: 'net.minecraftforge.gradle'
apply plugin: 'com.github.johnrengelman.shadow'
apply plugin: 'maven-publish'
apply plugin: 'com.jfrog.artifactory'
apply plugin: 'org.spongepowered.mixin'
apply plugin: 'idea'

version = version_major + "." + version_minor + "." + version_patch
group = "fr.dynamx"
archivesBaseName = "DynamX"

compileJava.options.encoding = 'UTF-8'

sourceCompatibility = targetCompatibility = compileJava.sourceCompatibility = compileJava.targetCompatibility = '1.8'

minecraft {
    mappings channel: 'stable', version: '39-1.12'

    accessTransformer = file('src/main/resources/META-INF/dynamx_at.cfg')

    runs {
        client {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'SCAN,REGISTRIES,REGISTRYDUMP'
            property 'forge.logging.console.level', 'info'
            jvmArg '-Dfml.coreMods.load=fr.dynamx.common.core.DynamXCoreMod'
        }
        server {
            property 'forge.logging.markers', 'SCAN,REGISTRIES,REGISTRYDUMP'
            property 'forge.logging.console.level', 'info'
            jvmArg '-Dfml.coreMods.load=fr.dynamx.common.core.DynamXCoreMod'
        }
    }
}

dependencies {
    minecraft 'net.minecraftforge:forge:1.12.2-14.23.5.2860'
    implementation ("net.minecraftforge:mergetool:0.2.3.3") { force = true }

    implementation 'com.github.stephengold:Libbulletjme:' + libbulletjme_version
    implementation "fr.aym.acsguis:ACsGuis:1.4.2-beta"
    implementation ":ModProtectionLib:1.5.3"
    implementation('org.spongepowered:mixin:0.7.11-SNAPSHOT') {
        exclude module: 'guava'
        exclude module: 'commons-io'
        exclude module: 'gson'
    }
    implementation 'org.joml:joml:1.10.5'
    compileOnly 'org.projectlombok:lombok:1.18.24'

    shadow('org.spongepowered:mixin:0.7.11-SNAPSHOT') {
        exclude module: 'guava'
        exclude module: 'commons-io'
        exclude module: 'gson'
    }
    shadow 'com.github.stephengold:Libbulletjme:' + libbulletjme_version
    shadow 'org.joml:joml:1.10.5'

    //shadow fileTree(dir: 'libs', include: '*.jar')
    //shadow ':ModProtectionLib:1.5.0'
    shadow fileTree(dir: 'libs', include: 'ModProtectionLib-1.5.3.jar')

    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.spongepowered:mixin:0.7.11-SNAPSHOT:processor'
    testAnnotationProcessor 'org.spongepowered:mixin:0.7.11-SNAPSHOT:processor'
}

mixin {
    config 'mixins.dynamxmod.json'
    add sourceSets.main, "refmap.dynamxmod.json"
}

jar {
    duplicatesStrategy 'exclude'
    manifest {
        attributes "FMLCorePlugin": "fr.dynamx.common.core.DynamXCoreMod"
        attributes "FMLAT": "dynamx_at.cfg"
        attributes "FMLCorePluginContainsFMLMod": "true"
        attributes "ForceLoadAsMod": "true"
        attributes "TweakClass": "org.spongepowered.asm.launch.MixinTweaker"
        attributes "TweakOrder": "0"
        attributes "MixinConfigs": "mixins.dynamxmod.json"
    }
}

shadowJar {
    duplicatesStrategy 'exclude'
    configurations = [project.configurations.shadow]
    classifier = 'all'
    exclude "module-info.class"
}

reobf {
    shadowJar {
    }
}

jar.finalizedBy('reobfJar')

/** Shit that idea needs to process resources ? **/
idea {
    module {
        inheritOutputDirs = true
    }
}
subprojects {
    apply plugin: 'idea'
}
task prepareAssets(type: Copy) {
    group = 'build'
    from project.file('src/main/resources')
    into project.file('build/classes/java/main')
}

classes.dependsOn(prepareAssets)
/** end of shit **/

tasks.reobfShadowJar.mustRunAfter shadowJar

task sourcesJar(type: Jar, dependsOn: classes) {
    from(sourceSets.main.allSource) {
        include 'fr/**/**'
    }
    classifier = 'sources'
}

build {
    dependsOn reobfShadowJar
    dependsOn sourcesJar
}


artifactoryPublish.dependsOn build

publishing {
    publications {
        mavenJava(MavenPublication) {
            artifactId = mavenArtifactId

            artifact(shadowJar){
                classifier = ''
            }
            artifact sourcesJar
        }
    }
}

artifactory {
    contextUrl = "${artifactory_contextUrl}"
    publish {
        repository {
            repoKey = 'DynamXRepo'
            username = "${artifactory_user}"
            password = "${artifactory_password}"
            maven = true
        }
        defaults {
            publications('mavenJava')
        }
    }
}