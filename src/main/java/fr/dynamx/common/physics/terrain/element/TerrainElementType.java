package fr.dynamx.common.physics.terrain.element;

import fr.dynamx.common.physics.terrain.chunk.ChunkTerrain;
import fr.dynamx.common.physics.terrain.computing.TerrainCollisionsCalculator;

/**
 * {@link ChunkTerrain} element types
 */
public enum TerrainElementType {
    /**
     * Both types
     */
    ALL,
    /**
     * Terrain generated by {@link TerrainCollisionsCalculator}
     */
    COMPUTED_TERRAIN,
    /**
     * Manually added terrain (see {@link fr.dynamx.common.physics.terrain.element.CustomSlopeTerrainElement})
     */
    PERSISTENT_ELEMENTS,
    /**
     * Reload all types from start when an incorrect {@link fr.dynamx.api.physics.terrain.ITerrainElement} was found
     */
    RELOAD_ALL
}
