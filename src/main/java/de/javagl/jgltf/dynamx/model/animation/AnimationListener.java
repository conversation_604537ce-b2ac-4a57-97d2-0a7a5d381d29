/*
 * www.javagl.de - JglTF
 *
 * Copyright 2015-2016 <PERSON> - http://www.javagl.de
 *
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 */
package de.javagl.jgltf.dynamx.model.animation;

/**
 * Interface for classes that want to be informed about the progress
 * of an {@link Animation}
 */
public interface AnimationListener
{
    /**
     * Will be called when the given {@link Animation} was updated.<br>
     * <br>
     * <b>Note:</b> The given array of interpolated output values MAY
     * be reused for multiple calls. Implementors of this method MUST NOT
     * store or modify the given array.
     *  
     * @param source The source {@link Animation}
     * @param timeS The time, in seconds
     * @param values The interpolated values for the given time
     */
    void animationUpdated(
        Animation source, float timeS, float values[]);
}