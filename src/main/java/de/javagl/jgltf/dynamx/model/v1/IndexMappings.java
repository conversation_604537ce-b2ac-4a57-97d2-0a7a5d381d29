/*
 * www.javagl.de - JglTF
 *
 * Copyright 2015-2017 <PERSON> - http://www.javagl.de
 *
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 */
package de.javagl.jgltf.dynamx.model.v1;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Utility methods to compute index mappings
 */
class IndexMappings
{
    /**
     * Compute the index mapping for the given map. If the given map is 
     * <code>null</code>, then an empty map will be returned. Otherwise,
     * the method will iterate through the given map, and return a 
     * map where the keys of the given map are associated with 
     * consecutive integers, starting with 0, in iteration order.
     * 
     * @param map The input map
     * @return The index mapping
     */
    static Map<String, Integer> computeIndexMapping(Map<String, ?> map)
    {
        if (map == null)
        {
            return Collections.emptyMap();
        }
        Map<String, Integer> indexMapping = 
            new LinkedHashMap<String, Integer>();
        int indexCounter = 0;
        for (String key : map.keySet())
        {
            indexMapping.put(key, indexCounter);
            indexCounter++;
        }
        return indexMapping;
    }
    
    /**
     * Private constructor to prevent instantiation
     */
    private IndexMappings()
    {
        // Private constructor to prevent instantiation
    }
}
