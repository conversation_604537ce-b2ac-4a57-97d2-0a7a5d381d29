itemGroup.dynamxmod_vehicle=DynamX Vehicle Tab
itemGroup.dynamxmod_object=DynamX Object Tab

item.dynamxmod.slopes.name=Slopes tool
item.dynamxmod.wrench.name=Wrench
item.dynamxmod.ragdoll.name=Ragdoll spawner
item.dynamxmod.shockwave.name=Shockwave generator

key.categories.dynamxmod=DynamX
key.brake=Handbrake
key.camin=Camera zoom in
key.camout=Camera zoom out
key.cammode=Change camera mode
key.watchbehind=Watch behind
key.debug=Debug menu
key.speedlimit=Speed limiter
key.startEngine=Engine on/off
key.pickobject=Pick object
key.takeobject=Take object
key.toggleLockDoor=Toogle lock door
key.attachTrailer=Attach trailer
key.powerup=Power up (helicopter)
key.powerdown=Power down (helicopter)
key.helicopter_pitch_forward=Pitch forward (helicopter)
key.helicopter_pitch_backward=Pitch backward (helicopter)
key.helicopter_yaw_left=Yaw left (helicopter)
key.helicopter_yaw_right=Yaw right (helicopter)
key.lock_rotation=Lock camera rotation (helicopter)

slopes.config.title=Config automatic slopes :
slopes.config.dalls=Ignore slabs
slopes.config.diags=Invert diagonals
slopes.config.orientation=Apply facing : %s
slopes.config.blocks=Blocks to ignore :
slopes.config.add=Add
slopes.config.blocknotfound=Block not found
slopes.config.cancel=Cancel
slopes.config.apply=Apply

slopes.clear.create=§a[CREATE] The selection slope has been reset
slopes.clear.delete=§d[DELETE] The selection region has been reset
slopes.clear.auto=§6[AUTO] The selection region has been reset

slopes.changemode.create=§aMode set to [CREATE]
slopes.changemode.delete=§dMode set to [DELETE]
slopes.changemode.auto=§6Mode set to [AUTO]

cmd.slopes.noitem=§cYou must have the slopes item in the main hand !
cmd.slopes.needcreate=§cYou must be in [CREATE] mode to create a slope !
cmd.slopes.create.points=§4You must select at least 4 points to create a slope
cmd.slopes.create.error=§4No slope have been added, review the order of the points !
cmd.slopes.create.terrainerror=§4Failed to load the terrain, load it with a vehicle then retry !
cmd.slopes.create.result=%s [CREATE] Computed and added slopes in %s ms
cmd.slopes.create.resulterror=§4An error occured while loading the terrain, delete the slopes of the zone then retry
cmd.slopes.create.success=§a[CREATE] The slopes have been added !
cmd.slopes.needdelete=§cYou must be in [DELETE] mode to delete slopes !
cmd.slopes.delete.points=§c[DELETE] You didn't set the two points of delimitation
cmd.slopes.delete.terrainerror=§4Failed to load the terrain of chunk %s, load it with a vehicle then retry !
cmd.slopes.delete.selerror=§4At least one slope have been found in the zone, but the zone doesn't overlap all the slope
cmd.slopes.delete.selempty=§4No slope have been found in the zone
cmd.slopes.delete.result=%s slope(s) in the selected zone have been deleted !
cmd.slopes.needauto=§6You must be in [AUTO] mode to generate slopes !
cmd.slopes.auto.points=§6[AUTO] You didn't set the two points of delimitation
cmd.slopes.auto.working=§6[AUTO] Generating slopes...
cmd.slopes.auto.error=§4No slope have been create, check the facing and the configuration of the blocks supporting the slopes !
cmd.slopes.auto.result=§6[AUTO] Generation of the slopes done in %s ms
cmd.slopes.auto.success=§a[AUTO] The slopes have been added !

hud.car.speedlimit=Speed limit : %s


dynamx.error.required_property=Required properties
dynamx.error.obj_duplicated_custom_textures=Duplicated custom textures
dynamx.error.armor_error=Bad armor parts
dynamx.error.updates=DynamX update available
dynamx.error.syntax_error=Syntax error
dynamx.error.deprecated_prop=Deprecated properties (that will be removed in future)
dynamx.error.deprecated_prop_format=props_ files are deprecated
dynamx.error.deprecated_light_format=Legacy Light parts are deprecated when using multiple lights on one part. It should now be declared in a 'MultiLight' sub property.
dynamx.error.duplicated_multi_light=You use multiple 'MultiLight' properties for the same object of the 3D model. You must merge them.
dynamx.error.missing_prop=Unknown pack file properties
dynamx.error.unknown_sub_info=Unknown sub properties
dynamx.error.deprecated_seat_config=Use the convention "Seat_Name" (instead of "NameSeat") to name your seats. In names:
dynamx.error.deprecated_door_config=Use the convention "Door_Name" (instead of "NameDoor") to name your doors. In names:
dynamx.error.sound_error=Invalid sound config
dynamx.error.config_error=Config error
dynamx.error.obj_none_material=Object(s) using the 'none' material of BlockBench
dynamx.error.mps_error=Cannot connect to DynamX server
dynamx.error.addon_init_error=Addon initialization error
dynamx.error.pack_requirements=Pack requirements not met
dynamx.error.collision_shape_error=Cannot load the physics collision shape
dynamx.error.complete_object_error=Cannot complete the object
dynamx.error.property_parse_error=Cannot parse the following properties. Check the documentation for the correct syntax.
dynamx.error.loading_tasks=Loading task error
dynamx.error.addon_load_error=Addon cannot be loaded !
dynamx.error.res_pack_load_fail=Resources (models and textures) detection failed
dynamx.error.pack_load_fail=Cannot load content pack
dynamx.error.missing_pack_info=Pack info is missing
dynamx.error.pack_file_load_error=Cannot load pack file
dynamx.error.addon_error=Addon loading error
dynamx.error.obj_error=Obj loading error
dynamx.error.wheel_invalid_suspaxis=Invalid wheel suspension axis
dynamx.error.too_many_variants=Too many material variants
dynamx.error.missing_depends_on_node=A node depends on a node in order to be renderer, but it doesn't exist:
dynamx.error.duplicate_scene=Duplicate part(s) for rendering:
dynamx.error.wheel_no_model=Wheel(s) has no separate 3D model, and no specified "Rim" object name to render.
dynamx.error.position_not_found_in_model=Cannot determine the position of the part from the 3D model and it is not set in the part config.
dynamx.error.seat_door_not_found=Seat's linked door not found
dynamx.error.invalid_view_type=Invalid item view type
dynamx.error.mixed_item_transforms_info=Mixed old "ItemTranslate/ItemRotate/ItemScale" and new "ItemTransforms" properties

dynamx.reload.models.errors=§c[DynamX] Some models have errors, use the debug menu to see them

trailer.wrench.first=Now click on the other vehicle
trailer.attach.fail=Cannot attach %s to %s
trailer.attached=Attached %s to %s
trailer.detached=§7The trailer has been detached
trailer.attach.toofar=§cThe attach points are too far away

wrench.mode.set.attach_objects=§cWrench mode set to attach objects
wrench.mode.set.attach_trailers=§aWrench mode set to attach trailer
wrench.mode.set.respawn_entities=§6Wrench mode set to respawn entity
wrench.mode.set.change_skins=§9Wrench mode set to change vehicle skin
wrench.mode.set.entity_seat=§dWrench mode set to put entity in seat
wrench.mode.set.none=§cWrench disabled
wrench.mode.set.launch_entities=§6Wrench mode set to launch props
wrench.mode.mode=Mode: %s
wrench.mode.attach_objects=§cAttach objects
wrench.mode.attach_trailers=§aAttach trailer
wrench.mode.respawn_entities=§6Respawn entity
wrench.mode.change_skins=§9Change vehicle skin
wrench.mode.entity_seat=§dPut entity in seat
wrench.mode.none=§cWrench disabled
wrench.mode.launch_entities=§6Launch props

dynamx.item.variant=Variant: %s
dynamx.item.description=Description: %s
dynamx.item.pack=Pack: %s