itemGroup.dynamxmod_vehicle=Véhicules DynamX
itemGroup.dynamxmod_object=Objets DynamX

item.dynamxmod.slopes.name=Outil pentes
item.dynamxmod.wrench.name=Clé à molette
item.dynamxmod.ragdoll.name=Spawner de ragdoll
item.dynamxmod.shockwave.name=Générateur d'onde de choc

key.categories.dynamxmod=DynamX
key.brake=Frein à main
key.camin=Zoom camera avant
key.camout=Zoom camera arrière
key.cammode=Changement du mode de la caméra
key.watchbehind=Regarder derrière
key.debug=Menu de debug
key.speedlimit=Limiteur de vitesse
key.startEngine=Allumer/éteindre le moteur
key.pickobject=Déplacer l'objet
key.takeobject=Prendre l'objet
key.toggleLockDoor=Ouvrir/fermer la portière
key.attachTrailer=Attacher la remorque
key.powerup=Augmenter la puissance (hélicoptère)
key.powerdown=Diminuer la puissance (hélicoptère)
key.helicopter_pitch_forward=Incliner vers l'avant (hélicoptère)
key.helicopter_pitch_backward=Incliner vers l'arrière (hélicoptère)
key.helicopter_yaw_left=Incliner vers la gauche (hélicoptère)
key.helicopter_yaw_right=Incliner vers la droite (hélicoptère)
key.lock_rotation=Verrouiller la caméra (hélicoptère)

slopes.config.title=Configuration des pentes automatiques :
slopes.config.dalls=Ignorer les demi dalles
slopes.config.diags=Inverser les diagonales
slopes.config.orientation=Appliquer l'orientation : %s
slopes.config.blocks=Blocs à ignorer :
slopes.config.add=Ajouter
slopes.config.blocknotfound=Ce block n'existe pas
slopes.config.cancel=Annuler
slopes.config.apply=Appliquer

slopes.clear.create=§a[CREATE] La pente de sélection a été réinitialisée
slopes.clear.delete=§d[DELETE] La région de sélection a été réinitialisée
slopes.clear.auto=§6[AUTO] La région de sélection a été réinitialisée

slopes.changemode.create=§aMode mis à [CREATE]
slopes.changemode.delete=§dMode mis à [DELETE]
slopes.changemode.auto=§6Mode mis à [AUTO]

cmd.slopes.noitem=§cVous devez avoir l'item slopes dans la main principale !
cmd.slopes.needcreate=§cVous devez être en mode [CREATE] pour créer une pente !
cmd.slopes.create.points=§4Vous devez avoir sélectionné au moins 4 points pour créer une pente
cmd.slopes.create.error=§4Aucune pente n'a pu être créée, vérifiez l'ordre des points !
cmd.slopes.create.terrainerror=§4Echec de chargement du terrain, chargez-le avec un véhicule et réessayez !
cmd.slopes.create.result=%s [CREATE] Calcul et ajout des pentes réalisés en %s ms
cmd.slopes.create.resulterror=§4Une erreur de chargement du terrain est survenue, effacez les pentes de la zone et réessayez
cmd.slopes.create.success=§a[CREATE] Les pentes ont bien été ajoutées !
cmd.slopes.needdelete=§cVous devez être en mode [DELETE] pour supprimer des pentes !
cmd.slopes.delete.points=§c[DELETE] Vous n'avez pas configuré les deux points de délimitation
cmd.slopes.delete.terrainerror=§4Echec de chargement du terrain du chunk %s, chargez-le avec un véhicule et réessayez !
cmd.slopes.delete.selerror=§4Au moins une pente a été trouvée dans la région, mais la région n'englobe pas toute la pente
cmd.slopes.delete.selempty=§4Aucune pente n'a été trouvée dans la région
cmd.slopes.delete.result=%s pente(s) dans la région de sélection ont été supprimées !
cmd.slopes.needauto=§6Vous devez être en mode [AUTO] pour générer des pentes !
cmd.slopes.auto.points=§6[AUTO] Vous n'avez pas configuré les deux points de délimitation
cmd.slopes.auto.working=§6[AUTO] Génération en cours...
cmd.slopes.auto.error=§4Aucune pente n'a pu être créée, vérifiez l'orientation et la config des blocs supportant des pentes !
cmd.slopes.auto.result=§6[AUTO] Génération et ajout des pentes réalisés en %s ms
cmd.slopes.auto.success=§a[AUTO] Les pentes ont bien été ajoutées !

hud.car.speedlimit=Limite de vitesse : %s

dynamx.reload.models.errors=§c[DynamX] Certains modèles ont des problèmes, utilisez le menu de debug pour les voir

trailer.wrench.first=Cliquez maintenant sur l'autre véhicule
trailer.attach.fail=Impossible d'attacher %s à %s
trailer.attached=%s attaché à %s
trailer.detached=§7La remorque a été détachée
trailer.attach.toofar=§cLes points d'attache sont trop éloignés

wrench.mode.set.attach_objects=§cLa clé à molette est en mode attacher des objets
wrench.mode.set.attach_trailers=§aLa clé à molette est en mode attacher la remorque
wrench.mode.set.respawn_entities=§6La clé à molette est en mode faire réapparaître les entités
wrench.mode.set.change_skins=§9La clé à molette est en mode changer le skin des véhicules
wrench.mode.set.entity_seat=§dLa clé à molette est en mode mettre des entités dans les véhicules
wrench.mode.set.none=§cLa clé à molette est désactivée
wrench.mode.set.launch_entities=§6La clé à molette est en mode lancer des props
wrench.mode.mode=Mode : %s
wrench.mode.attach_objects=§cAttacher des objets
wrench.mode.attach_trailers=§aAttacher la remorque
wrench.mode.respawn_entities=§6Faire réapparaître les entités
wrench.mode.change_skins=§9Changer le skin des véhicules
wrench.mode.entity_seat=§dMettre des entités dans les véhicules
wrench.mode.none=§cClé à molette désactivée
wrench.mode.launch_entities=§6Lancer des props

dynamx.item.variant=Variante : %s
dynamx.item.description=Description : %s
dynamx.item.pack=Pack : %s