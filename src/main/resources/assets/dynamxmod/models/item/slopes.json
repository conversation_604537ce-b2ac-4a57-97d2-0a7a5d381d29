{"credit": "⭐ Made By Green' ⭐", "textures": {"0": "blocks/concrete_red", "particle": "blocks/concrete_red"}, "elements": [{"from": [1, 0.00317, 0], "to": [2, 1, 15.03614], "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 15.1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 15.1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 15.1], "texture": "#0"}, "down": {"uv": [0, 0, 1, 15.1], "texture": "#0"}}}, {"from": [1, 1.00317, 15.03614], "to": [2, 7.22817, 16.01114], "rotation": {"angle": 0, "axis": "y", "origin": [0, 0.00317, 0.01114]}, "faces": {"north": {"uv": [0, 0, 1, 6.225], "texture": "#0"}, "east": {"uv": [0, 0, 0.975, 6.225], "texture": "#0"}, "south": {"uv": [0, 0, 1, 6.225], "texture": "#0"}, "west": {"uv": [0, 0, 0.975, 6.225], "texture": "#0"}, "up": {"uv": [0, 0, 1, 0.975], "texture": "#0"}, "down": {"uv": [0, 0, 1, 0.975], "texture": "#0"}}}, {"from": [2, 6.25317, 15.03614], "to": [13, 7.22817, 16.01114], "rotation": {"angle": 0, "axis": "y", "origin": [0, 0.00317, 0.01114]}, "faces": {"north": {"uv": [0, 0, 11, 0.975], "texture": "#0"}, "east": {"uv": [0, 0, 0.975, 0.975], "texture": "#0"}, "south": {"uv": [0, 0, 11, 0.975], "texture": "#0"}, "west": {"uv": [0, 0, 0.975, 0.975], "texture": "#0"}, "up": {"uv": [0, 0, 11, 0.975], "texture": "#0"}, "down": {"uv": [0, 0, 11, 0.975], "texture": "#0"}}}, {"from": [13, 1.00317, 15.03614], "to": [14, 7.22817, 16.01114], "rotation": {"angle": 0, "axis": "y", "origin": [0, 0.00317, 0.01114]}, "faces": {"north": {"uv": [0, 0, 1, 6.225], "texture": "#0"}, "east": {"uv": [0, 0, 0.975, 6.225], "texture": "#0"}, "south": {"uv": [0, 0, 1, 6.225], "texture": "#0"}, "west": {"uv": [0, 0, 0.975, 6.225], "texture": "#0"}, "up": {"uv": [0, 0, 1, 0.975], "texture": "#0"}, "down": {"uv": [0, 0, 1, 0.975], "texture": "#0"}}}, {"from": [1, 0.00317, 15.03614], "to": [14, 1.00317, 16.01114], "rotation": {"angle": 0, "axis": "y", "origin": [0, 0.00317, 0.01114]}, "faces": {"north": {"uv": [0, 0, 12, 1], "texture": "#0"}, "east": {"uv": [0, 0, 0.975, 1], "texture": "#0"}, "south": {"uv": [0, 0, 12, 1], "texture": "#0"}, "west": {"uv": [0, 0, 0.975, 1], "texture": "#0"}, "up": {"uv": [0, 0, 12, 0.975], "texture": "#0"}, "down": {"uv": [0, 0, 12, 0.975], "texture": "#0"}}}, {"from": [2, 0, 0], "to": [14, 1, 1], "faces": {"north": {"uv": [0, 0, 12, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 12, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 12, 1], "texture": "#0"}, "down": {"uv": [0, 0, 12, 1], "texture": "#0"}}}, {"from": [13, 0.00317, 0], "to": [14, 1, 15.03614], "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 15.1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 15.1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 15.1], "texture": "#0"}, "down": {"uv": [0, 0, 1, 15.1], "texture": "#0"}}}, {"from": [13, 3.09953, -0.41762], "to": [14, 4.09953, 15.85738], "rotation": {"angle": -22.5, "axis": "x", "origin": [14.5, 3.59953, 7.58238]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 16, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 16, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 16], "texture": "#0"}, "down": {"uv": [0, 0, 1, 16], "texture": "#0"}}}, {"from": [1, 3.09953, -0.41762], "to": [2, 4.09953, 15.85738], "rotation": {"angle": -22.5, "axis": "x", "origin": [14.5, 3.59953, 7.58238]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 16, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 16, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 16], "texture": "#0"}, "down": {"uv": [0, 0, 1, 16], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [26, 56, 0], "translation": [0, 2.25, 0.75], "scale": [0.33, 0.33, 0.33]}, "thirdperson_lefthand": {"rotation": [26, 56, 0], "translation": [0, 2.25, 0.75], "scale": [0.33, 0.33, 0.33]}, "firstperson_righthand": {"rotation": [0, 56, 0], "translation": [-0.75, 3.75, 0.25], "scale": [0.35, 0.35, 0.35]}, "firstperson_lefthand": {"rotation": [0, 56, 0], "translation": [-0.75, 3.75, 0.25], "scale": [0.35, 0.35, 0.35]}, "ground": {"translation": [0, 2, 0], "scale": [0.5, 0.5, 0.5]}, "gui": {"rotation": [21, 137, 0], "translation": [-0.25, 3, 0], "scale": [0.73, 0.73, 0.73]}, "head": {"rotation": [0, 180, 0], "translation": [0, 13, 7]}, "fixed": {"rotation": [0, 180, 0]}}}